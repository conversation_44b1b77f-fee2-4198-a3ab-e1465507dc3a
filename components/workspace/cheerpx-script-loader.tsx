'use client';

import <PERSON>ript from "next/script";
import { useEffect, useState } from "react";

export function CheerpXScriptLoader() {
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const [scriptError, setScriptError] = useState(false);

  useEffect(() => {
    // Check if CheerpX is already loaded
    if (typeof window !== 'undefined' && (window as any).CheerpX) {
      console.log('CheerpX already available');
      setScriptLoaded(true);
    }
  }, []);

  const handleScriptLoad = () => {
    console.log('CheerpX script loaded successfully');
    setScriptLoaded(true);
    setScriptError(false);
    
    if (typeof window !== 'undefined' && (window as any).CheerpX) {
      console.log('CheerpX is available:', (window as any).CheerpX);
    } else {
      console.warn('CheerpX script loaded but CheerpX object not found');
    }
  };

  const handleScriptError = (e: any) => {
    console.error('Failed to load CheerpX script from primary URL:', e);
    setScriptError(true);
    
    // Try fallback URL
    const fallbackScript = document.createElement('script');
    fallbackScript.src = 'https://cxrtnc.leaningtech.com/cx.js';
    fallbackScript.onload = () => {
      console.log('CheerpX loaded from fallback URL');
      setScriptLoaded(true);
      setScriptError(false);
    };
    fallbackScript.onerror = () => {
      console.error('Failed to load CheerpX from fallback URL');
      setScriptError(true);
    };
    document.head.appendChild(fallbackScript);
  };

  return (
    <>
      {/* Load CheerpX script - try multiple versions */}
      <Script
        src="https://cxrtnc.leaningtech.com/1.0.0/cx.js"
        strategy="beforeInteractive"
        onLoad={handleScriptLoad}
        onError={handleScriptError}
      />

      {/* Fallback script if primary fails */}
      {scriptError && !scriptLoaded && (
        <Script
          src="https://cxrtnc.leaningtech.com/cx.js"
          strategy="afterInteractive"
          onLoad={() => {
            console.log('CheerpX loaded from fallback URL');
            setScriptLoaded(true);
            setScriptError(false);
          }}
          onError={() => {
            console.error('All CheerpX script sources failed');
          }}
        />
      )}
      
      {/* Set required headers for cross-origin isolation */}
      <Script id="cheerpx-headers" strategy="beforeInteractive">
        {`
          // Set up cross-origin isolation if not already set
          if (typeof SharedArrayBuffer === 'undefined') {
            console.warn('SharedArrayBuffer is not available. Cross-origin isolation may not be properly configured.');
          } else {
            console.log('SharedArrayBuffer is available - cross-origin isolation is working');
          }
          
          // Global flag to indicate CheerpX script loading status
          window.__CHEERPX_LOADING__ = true;
        `}
      </Script>
    </>
  );
}
