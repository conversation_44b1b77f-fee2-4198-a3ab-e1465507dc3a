"use client";

import { useState, useEffect } from "react";
import { Cloud, Wifi, WifiOff, AlertCircle, CheckCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface StatusIndicatorProps {
  className?: string;
}

interface SystemStatus {
  online: boolean;
  cloudConnected: boolean;
  lastSync: Date | null;
  instancesRunning: number;
  workspacesActive: number;
}

export function StatusIndicator({ className }: StatusIndicatorProps) {
  const [status, setStatus] = useState<SystemStatus>({
    online: true,
    cloudConnected: true,
    lastSync: new Date(),
    instancesRunning: 0,
    workspacesActive: 0,
  });

  useEffect(() => {
    // Simulate status updates
    const interval = setInterval(() => {
      setStatus(prev => ({
        ...prev,
        lastSync: new Date(),
        // Simulate some random data
        instancesRunning: Math.floor(Math.random() * 5),
        workspacesActive: Math.floor(Math.random() * 3),
      }));
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    if (!status.online) return "bg-red-500";
    if (!status.cloudConnected) return "bg-yellow-500";
    return "bg-green-500";
  };

  const getStatusText = () => {
    if (!status.online) return "Offline";
    if (!status.cloudConnected) return "Cloud Disconnected";
    return "All Systems Operational";
  };

  const getStatusIcon = () => {
    if (!status.online) return WifiOff;
    if (!status.cloudConnected) return AlertCircle;
    return CheckCircle;
  };

  const StatusIcon = getStatusIcon();

  return (
    <TooltipProvider>
      <div className={`flex items-center gap-2 text-xs text-muted-foreground ${className}`}>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-2">
              <Cloud className="h-3 w-3" />
              <span className="hidden sm:inline">Connected</span>
              <div className={`h-2 w-2 rounded-full ${getStatusColor()}`} />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <StatusIcon className="h-3 w-3" />
                <span>{getStatusText()}</span>
              </div>
              {status.lastSync && (
                <div className="text-xs text-muted-foreground">
                  Last sync: {status.lastSync.toLocaleTimeString()}
                </div>
              )}
              <div className="space-y-1 text-xs">
                <div>Running instances: {status.instancesRunning}</div>
                <div>Active workspaces: {status.workspacesActive}</div>
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
}
