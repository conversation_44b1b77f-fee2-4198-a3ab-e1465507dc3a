"use client";

import { Plus, Package, Monitor, Server, Zap, FolderOpen } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuShortcut,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";

interface QuickAction {
  title: string;
  description: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  shortcut?: string;
  group: string;
}

const quickActions: QuickAction[] = [
  {
    title: "New Project",
    description: "Create a new project",
    href: "/dashboard/projects/new",
    icon: Package,
    shortcut: "⌘⇧P",
    group: "Create",
  },
  {
    title: "New Workspace",
    description: "Set up a development workspace",
    href: "/dashboard/workspaces/new",
    icon: Monitor,
    shortcut: "⌘⇧W",
    group: "Create",
  },
  {
    title: "Launch Instance",
    description: "Start a new WebVM instance",
    href: "/dashboard/instances/new",
    icon: Server,
    shortcut: "⌘⇧I",
    group: "Create",
  },
  {
    title: "Quick Deploy",
    description: "Deploy from template",
    href: "/dashboard/templates",
    icon: Zap,
    shortcut: "⌘⇧D",
    group: "Deploy",
  },
  {
    title: "Import Project",
    description: "Import existing project",
    href: "/dashboard/projects/import",
    icon: FolderOpen,
    group: "Import",
  },
];

export function QuickActions() {
  const groupedActions = quickActions.reduce((acc, action) => {
    if (!acc[action.group]) {
      acc[action.group] = [];
    }
    acc[action.group].push(action);
    return acc;
  }, {} as Record<string, QuickAction[]>);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Plus className="h-4 w-4" />
          <span className="hidden sm:inline">New</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {Object.entries(groupedActions).map(([group, actions], groupIndex) => (
          <div key={group}>
            {groupIndex > 0 && <DropdownMenuSeparator />}
            <DropdownMenuGroup>
              <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">
                {group}
              </DropdownMenuLabel>
              {actions.map((action) => (
                <DropdownMenuItem key={action.title} asChild>
                  <Link href={action.href} className="flex items-center">
                    <action.icon className="h-4 w-4 mr-3" />
                    <div className="flex-1">
                      <div className="font-medium">{action.title}</div>
                      <div className="text-xs text-muted-foreground">
                        {action.description}
                      </div>
                    </div>
                    {action.shortcut && (
                      <DropdownMenuShortcut>{action.shortcut}</DropdownMenuShortcut>
                    )}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </div>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
