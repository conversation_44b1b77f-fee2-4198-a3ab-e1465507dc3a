import { PrismaClient, UserR<PERSON>, MemberRole, TaskStatus, TaskPriority, WorkspaceStatus } from '../lib/generated/prisma';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seeding...');

  // Create admin user
  const adminPassword = await hash('T3chn0l0gy@1', 10);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Thando Admin',
      password: adminPassword,
      role: UserRole.SUPER_ADMIN,
      preferences: {
        create: {
          theme: 'system',
        },
      },
    },
  });
  console.log(`Created admin user: ${admin.email}`);

  // Create regular users
  const regularUsers = [];
  for (let i = 1; i <= 3; i++) {
    const password = await hash(`User${i}Password!`, 10);
    const user = await prisma.user.upsert({
      where: { email: `user${i}@example.com` },
      update: {},
      create: {
        email: `user${i}@example.com`,
        name: `Test User ${i}`,
        password,
        role: UserRole.USER,
        preferences: {
          create: {
            theme: 'system',
          },
        },
      },
    });
    regularUsers.push(user);
    console.log(`Created regular user: ${user.email}`);
  }

  // Create organizations
  const organizations = [];
  const orgNames = ['Acme Inc', 'Tech Solutions', 'Creative Studio'];
  
  for (let i = 0; i < orgNames.length; i++) {
    const org = await prisma.organization.upsert({
      where: { slug: orgNames[i].toLowerCase().replace(/\s+/g, '-') },
      update: {},
      create: {
        name: orgNames[i],
        slug: orgNames[i].toLowerCase().replace(/\s+/g, '-'),
        image: `https://ui-avatars.com/api/?name=${encodeURIComponent(orgNames[i])}&background=random`,
      },
    });
    organizations.push(org);
    console.log(`Created organization: ${org.name}`);

    // Add admin as owner to all organizations
    await prisma.organizationMember.upsert({
      where: {
        organizationId_userId: {
          organizationId: org.id,
          userId: admin.id,
        },
      },
      update: {},
      create: {
        organizationId: org.id,
        userId: admin.id,
        role: MemberRole.OWNER,
      },
    });
    console.log(`Added admin as owner to: ${org.name}`);

    // Add regular users as members to organizations
    for (let j = 0; j < regularUsers.length; j++) {
      // Skip some users for some orgs to create variety
      if (i === j) continue;
      
      const role = j === 0 ? MemberRole.ADMIN : MemberRole.MEMBER;
      await prisma.organizationMember.upsert({
        where: {
          organizationId_userId: {
            organizationId: org.id,
            userId: regularUsers[j].id,
          },
        },
        update: {},
        create: {
          organizationId: org.id,
          userId: regularUsers[j].id,
          role,
        },
      });
      console.log(`Added ${regularUsers[j].name} as ${role} to: ${org.name}`);
    }

    // Create projects for each organization
    const projectCount = Math.floor(Math.random() * 3) + 1; // 1-3 projects per org
    for (let p = 0; p < projectCount; p++) {
      const projectName = `${org.name} Project ${p + 1}`;
      const project = await prisma.project.upsert({
        where: {
          id: `project-${org.id}-${p}`,
        },
        update: {},
        create: {
          id: `project-${org.id}-${p}`,
          name: projectName,
          description: `This is a sample project for ${org.name}`,
          organizationId: org.id,
        },
      });
      console.log(`Created project: ${project.name}`);

      // Create tasks for each project
      const taskStatuses = [TaskStatus.TODO, TaskStatus.IN_PROGRESS, TaskStatus.DONE];
      const taskPriorities = [TaskPriority.LOW, TaskPriority.MEDIUM, TaskPriority.HIGH];
      
      const taskCount = Math.floor(Math.random() * 5) + 3; // 3-7 tasks per project
      for (let t = 0; t < taskCount; t++) {
        const status = taskStatuses[Math.floor(Math.random() * taskStatuses.length)];
        const priority = taskPriorities[Math.floor(Math.random() * taskPriorities.length)];
        
        // Randomly assign to a user or leave unassigned
        const assigneeId = Math.random() > 0.3 
          ? [...regularUsers, admin][Math.floor(Math.random() * (regularUsers.length + 1))].id 
          : null;
        
        const task = await prisma.task.create({
          data: {
            title: `Task ${t + 1} for ${project.name}`,
            description: `This is a sample task with ${priority} priority and ${status} status`,
            status,
            priority,
            projectId: project.id,
            assigneeId,
            dueDate: new Date(Date.now() + Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000), // Random due date within next 30 days
          },
        });
        console.log(`Created task: ${task.title}`);
      }

      // Create workspaces for each project
      const workspaceCount = Math.floor(Math.random() * 2) + 1; // 1-2 workspaces per project
      for (let w = 0; w < workspaceCount; w++) {
        const workspaceName = `${project.name} Workspace ${w + 1}`;
        const workspace = await prisma.workspace.create({
          data: {
            name: workspaceName,
            description: `Development environment for ${project.name}`,
            projectId: project.id,
            status: w === 0 ? WorkspaceStatus.ACTIVE : WorkspaceStatus.INACTIVE,
            config: {
              theme: "dark",
              autoSave: true,
              fontSize: 14
            },
          },
        });
        console.log(`Created workspace: ${workspace.name}`);

        // Create WebVM instances for active workspaces
        if (workspace.status === WorkspaceStatus.ACTIVE) {
          const instance = await prisma.webVMInstance.create({
            data: {
              name: `${workspace.name} VM`,
              workspaceId: workspace.id,
              imageUrl: "https://example.com/cheerpx-image.wasm",
              config: {
                cpuCores: 2,
                memoryMB: 4096,
                diskGB: 10
              },
              resources: {
                cpu: "2",
                memory: "4GB",
                disk: "10GB"
              },
              networkConfig: {
                type: "bridged",
                ipAddress: "auto"
              },
              connectionUrl: `https://webvm.example.com/${workspace.id}`,
            },
          });
          console.log(`Created WebVM instance: ${instance.name}`);

          // Create some metrics for the instance
          const metricTypes = ["CPU_USAGE", "MEMORY_USAGE", "DISK_USAGE", "NETWORK_IN", "NETWORK_OUT"];
          const units = ["percent", "MB", "GB", "Mbps", "ms"];
          
          for (let m = 0; m < metricTypes.length; m++) {
            // Create 5 data points for each metric type
            for (let d = 0; d < 5; d++) {
              await prisma.webVMMetric.create({
                data: {
                  instanceId: instance.id,
                  metricType: metricTypes[m] as any,
                  value: Math.random() * 100,
                  unit: units[m],
                  timestamp: new Date(Date.now() - d * 3600 * 1000), // Last few hours
                },
              });
            }
            console.log(`Created metrics for ${metricTypes[m]}`);
          }
        }
      }
    }
  }

  console.log('Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });