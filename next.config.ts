import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable cross-origin isolation for SharedArrayBuffer support (required for WebVM/CheerpX)
  async headers() {
    return [
      {
        // Apply to all routes
        source: '/(.*)',
        headers: [
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'require-corp'
          },
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin'
          }
        ]
      }
    ];
  },

  // Experimental features for WebAssembly support
  experimental: {
    // WebAssembly support is handled through webpack configuration below
  },

  // Webpack configuration for WebAssembly
  webpack: (config, { isServer }) => {
    // Add WebAssembly support
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
      syncWebAssembly: true,
    };

    // Handle .wasm files
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'webassembly/async',
    });

    // Don't parse WebAssembly files on the server
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        'sharp': 'commonjs sharp',
      });
    }

    return config;
  },

  devIndicators: false,
};

export default nextConfig;
