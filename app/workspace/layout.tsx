import { auth } from "@/auth";
import { redirect } from "next/navigation";
import Script from "next/script";

export default async function WorkspaceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  if (!session?.user) {
    redirect("/auth/login");
  }

  return (
    <>
      {/* Load CheerpX script */}
      <Script
        src="https://cxrtnc.leaningtech.com/1.0.0/cx.js"
        strategy="beforeInteractive"
        onLoad={() => {
          console.log('CheerpX script loaded successfully');
          if (typeof window !== 'undefined' && window.CheerpX) {
            console.log('CheerpX is available:', window.CheerpX);
          }
        }}
        onError={(e) => {
          console.error('Failed to load CheerpX script from primary URL:', e);
          // Try fallback URL
          const fallbackScript = document.createElement('script');
          fallbackScript.src = 'https://cxrtnc.leaningtech.com/cx.js';
          fallbackScript.onload = () => console.log('CheerpX loaded from fallback URL');
          fallbackScript.onerror = () => console.error('Failed to load CheerpX from fallback URL');
          document.head.appendChild(fallbackScript);
        }}
      />

      {/* Set required headers for cross-origin isolation */}
      <Script id="cheerpx-headers" strategy="beforeInteractive">
        {`
          // Set up cross-origin isolation if not already set
          if (typeof SharedArrayBuffer === 'undefined') {
            console.warn('SharedArrayBuffer is not available. Cross-origin isolation may not be properly configured.');
          }
        `}
      </Script>

      <div className="h-screen w-screen overflow-hidden bg-background">
        {children}
      </div>
    </>
  );
}
