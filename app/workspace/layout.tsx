import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { CheerpXScriptLoader } from "@/components/workspace/cheerpx-script-loader";

export default async function WorkspaceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  if (!session?.user) {
    redirect("/auth/login");
  }

  return (
    <>
      <CheerpXScriptLoader />
      <div className="h-screen w-screen overflow-hidden bg-background">
        {children}
      </div>
    </>
  );
}
