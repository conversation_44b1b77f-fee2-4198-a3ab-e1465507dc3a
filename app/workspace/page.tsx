import { Metada<PERSON> } from "next";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { WorkspaceContainer } from "@/components/workspace/workspace-container";

export const metadata: Metadata = {
  title: "WebVM Workspace",
  description: "Full-screen development environment powered by CheerpX WebVM",
};

interface WorkspacePageProps {
  searchParams: {
    projectId?: string;
    workspaceId?: string;
  };
}

export default async function WorkspacePage({ searchParams }: WorkspacePageProps) {
  const session = await auth();

  if (!session?.user?.id) {
    redirect("/auth/login");
  }

  return (
    <WorkspaceContainer 
      userId={session.user.id}
      projectId={searchParams.projectId}
      workspaceId={searchParams.workspaceId}
    />
  );
}
