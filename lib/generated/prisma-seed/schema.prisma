// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

// This tells Prisma to run the seed script after a database reset
generator seeder {
  provider        = "prisma-client-js"
  output          = "../lib/generated/prisma-seed"
  previewFeatures = ["fullTextSearch", "fullTextIndex"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(USER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts      Account[]
  sessions      Session[]
  organizations OrganizationMember[]
  assignedTasks Task[]

  // User preferences
  preferences UserPreference?
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// User preferences
model UserPreference {
  id        String   @id @default(cuid())
  userId    String   @unique
  theme     String   @default("system") // light, dark, system
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Organization models for multi-tenancy
model Organization {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  image     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  members     OrganizationMember[]
  projects    Project[]
  invitations OrganizationInvitation[]
}

model OrganizationMember {
  id             String     @id @default(cuid())
  organizationId String
  userId         String
  role           MemberRole @default(MEMBER)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
}

model OrganizationInvitation {
  id             String     @id @default(cuid())
  email          String
  organizationId String
  role           MemberRole @default(MEMBER)
  token          String     @unique
  expires        DateTime
  createdAt      DateTime   @default(now())

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
}

// Project models
model Project {
  id             String   @id @default(cuid())
  name           String
  description    String?
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  tasks        Task[]
  workspaces   Workspace[]
}

model Task {
  id          String       @id @default(cuid())
  title       String
  description String?
  status      TaskStatus   @default(TODO)
  priority    TaskPriority @default(MEDIUM)
  projectId   String
  assigneeId  String?
  dueDate     DateTime?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  project  Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  assignee User?   @relation(fields: [assigneeId], references: [id], onDelete: SetNull)
}

// Enums
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum MemberRole {
  OWNER
  ADMIN
  MEMBER
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  DONE
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
}

// Workspace models for development environments
model Workspace {
  id          String          @id @default(cuid())
  name        String
  description String?
  projectId   String
  status      WorkspaceStatus @default(INACTIVE)
  config      Json? // Workspace configuration (IDE settings, environment variables, etc.)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  project   Project         @relation(fields: [projectId], references: [id], onDelete: Cascade)
  instances WebVMInstance[]
}

// WebVM Instance models for CheerpX containers
model WebVMInstance {
  id            String      @id @default(cuid())
  name          String
  workspaceId   String
  status        WebVMStatus @default(STOPPED)
  imageUrl      String? // CheerpX image URL
  config        Json? // CheerpX configuration
  resources     Json? // Resource allocation (CPU, memory, etc.)
  networkConfig Json? // Network configuration
  connectionUrl String? // WebVM connection URL
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  startedAt     DateTime?
  stoppedAt     DateTime?

  workspace Workspace     @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  metrics   WebVMMetric[]
}

// WebVM Metrics for monitoring
model WebVMMetric {
  id         String     @id @default(cuid())
  instanceId String
  metricType MetricType
  value      Float
  unit       String
  timestamp  DateTime   @default(now())

  instance WebVMInstance @relation(fields: [instanceId], references: [id], onDelete: Cascade)

  @@index([instanceId, timestamp])
}

// Additional enums
enum WorkspaceStatus {
  ACTIVE
  INACTIVE
  ARCHIVED
  ERROR
}

enum WebVMStatus {
  STARTING
  RUNNING
  STOPPING
  STOPPED
  ERROR
  SUSPENDED
}

enum MetricType {
  CPU_USAGE
  MEMORY_USAGE
  DISK_USAGE
  NETWORK_IN
  NETWORK_OUT
  RESPONSE_TIME
}
